# Vietnamese Address Extraction Tool

A powerful tool for extracting and structuring Vietnamese address components using Google's Generative AI with optional grounding search capabilities.

## Features

- 🏠 **Comprehensive Address Parsing**: Extracts unit, floor, building, street number, street name, and ward/district information
- 🔍 **Smart Grounding Search**: Uses Google Search to find missing address components
- ⚡ **Async Processing**: High-performance concurrent processing with configurable limits
- 📊 **Multiple Output Formats**: Supports Excel (.xlsx) and JSON output
- 🔧 **Configurable**: Centralized configuration via environment variables
- 📈 **Batch Processing**: Processes large datasets with intermediate saves
- 📋 **Detailed Reports**: Comprehensive extraction summaries and statistics

## Setup Instructions

### 1. Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```


```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```properties
# API Configuration
GOOGLE_API_KEY="your-google-api-key-here"

# Model Configuration (optional)
MODEL_NAME="gemini-2.0-flash"

# Processing Configuration (optional)
MAX_CONCURRENT_REQUESTS=10
BATCH_SIZE=1000

# Default File Configuration (optional)
DEFAULT_EXCEL_FILE="savills-address.xlsx"
DEFAULT_ADDRESS_COLUMN="Full_Address"
DEFAULT_OUTPUT_FILE="extracted_addresses.xlsx"

# Grounding Configuration (optional)
USE_GROUNDING_BY_DEFAULT=True
```

## Usage

### Basic Usage

Process default Excel file with default settings:
```bash
python main.py
```

### Custom File and Column

Process custom Excel file with specific column:
```bash
python main.py --excel-file "my-addresses.xlsx" --address-column "Address_Column"
```

### Adjust Concurrency

Control the number of concurrent API requests:
```bash
python main.py --max-concurrent 20
```

### Process Limited Records

Process only first 100 addresses:
```bash
python main.py --limit 100
```

### Disable Grounding Search

Process without Google Search grounding:
```bash
python main.py --no-grounding
```

### Single Address Processing

Extract components from a single address:
```bash
python main.py --single-address "123 Nguyen Trai, Phuong Ben Nghe, Quan 1, Ho Chi Minh City"
```

### Custom Output File

Specify custom output file:
```bash
python main.py --output "results.xlsx"
```

### Show Current Configuration

Display current configuration settings:
```bash
python main.py --show-config
```

## Command Line Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--excel-file` | `-f` | Excel file containing addresses | From config |
| `--address-column` | `-c` | Column name containing addresses | From config |
| `--limit` | `-l` | Limit number of addresses to process | None |
| `--output` | `-o` | Output file (.json or .xlsx) | From config |
| `--no-grounding` | | Disable Google Search grounding | False |
| `--single-address` | `-s` | Process single address | None |
| `--max-concurrent` | `-m` | Maximum concurrent API requests | From config |
| `--show-config` | | Show current configuration | False |

## Configuration Options

All configuration can be set via environment variables in the `.env` file:

### API Configuration
- `GOOGLE_API_KEY`: Your Google API key (required)

### Model Configuration
- `MODEL_NAME`: Google GenAI model name (default: "gemini-2.0-flash")

### Processing Configuration
- `MAX_CONCURRENT_REQUESTS`: Maximum concurrent API requests (default: 10)
- `BATCH_SIZE`: Number of addresses per batch (default: 1000)

### File Configuration
- `DEFAULT_EXCEL_FILE`: Default input Excel file (default: "savills-address.xlsx")
- `DEFAULT_ADDRESS_COLUMN`: Default address column name (default: "Full_Address")
- `DEFAULT_OUTPUT_FILE`: Default output file (default: "extracted_addresses.xlsx")

### Features Configuration
- `USE_GROUNDING_BY_DEFAULT`: Enable grounding search by default (default: True)

## Output Format

### Excel Output
The tool generates an Excel file with the following columns:
- `original_address`: Original address text
- `row_index`: Original row number from input file
- `success`: Whether extraction was successful
- `extracted_unit`: Unit/apartment number
- `extracted_floor`: Floor number
- `extracted_building`: Building/tower name
- `extracted_street_number`: Street/house number
- `extracted_street_name`: Street name
- `extracted_ward_or_district`: Ward and district information
- `extracted_searched_fields`: Fields that were enhanced via grounding search
- `extracted_confidence_level`: Extraction confidence (High/Mid/Low)

### JSON Output
When using JSON output, each record includes:
```json
{
  "original_address": "123 Nguyen Trai, Ben Nghe Ward, District 1",
  "row_index": 0,
  "success": true,
  "extracted_data": {
    "unit": null,
    "floor": null,
    "building": null,
    "street_number": "123",
    "street_name": "Nguyen Trai",
    "ward_or_district": "Ben Nghe Ward, District 1",
    "searched_fields": ["ward_or_district"],
    "confidence_level": "High"
  }
}
```

## Performance Tips

1. **Adjust Concurrency**: Start with 10 concurrent requests and increase based on your API limits
2. **Use Batching**: Large datasets are automatically processed in batches with intermediate saves
3. **Monitor Rate Limits**: Watch for API rate limit errors and reduce concurrency if needed
4. **Grounding Search**: Disable grounding (`--no-grounding`) for faster processing if high accuracy isn't required

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure `GOOGLE_API_KEY` is set correctly in `.env`
2. **Column Not Found**: Check that `--address-column` matches your Excel file column name
3. **Rate Limits**: Reduce `--max-concurrent` value if hitting API limits
4. **Memory Issues**: Reduce `BATCH_SIZE` in `.env` for large datasets

### Getting Help

Run with `--help` to see all available options:
```bash
python main.py --help
```

## License

This project is licensed under the MIT License.