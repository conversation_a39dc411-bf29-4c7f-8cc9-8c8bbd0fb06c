import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for environment variables and default settings"""

    # Azure API Configuration
    AZURE_API_KEY = os.getenv('AZURE_API_KEY')
    AZURE_ENDPOINT = os.getenv('AZURE_ENDPOINT')
    AZURE_API_VERSION = os.getenv('AZURE_API_VERSION', '2025-01-01-preview')
    AZURE_DEPLOYMENT_NAME = os.getenv('AZURE_DEPLOYMENT_NAME')

    # Legacy Google API Configuration (deprecated)
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')

    # Model Configuration
    MODEL_NAME = os.getenv('MODEL_NAME', 'gpt-4')
    
    # Processing Configuration
    MAX_CONCURRENT_REQUESTS = int(os.getenv('MAX_CONCURRENT_REQUESTS', '10'))
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', '1000'))
    INTERMEDIATE_SAVE_INTERVAL = int(os.getenv('INTERMEDIATE_SAVE_INTERVAL', '500'))
    
    # Default File Configuration
    DEFAULT_EXCEL_FILE = os.getenv('DEFAULT_EXCEL_FILE', 'savills-address.xlsx')
    DEFAULT_ADDRESS_COLUMN = os.getenv('DEFAULT_ADDRESS_COLUMN', 'Full_Address')
    DEFAULT_OUTPUT_FILE = os.getenv('DEFAULT_OUTPUT_FILE', 'extracted_addresses.xlsx')
    
    # Grounding Configuration (deprecated - search grounding removed)
    USE_GROUNDING_BY_DEFAULT = False
    
    @staticmethod
    def get_azure_api_key():
        """Get Azure API key from environment"""
        if not Config.AZURE_API_KEY:
            raise ValueError(
                "AZURE_API_KEY not found in environment variables. "
                "Please check your .env file."
            )
        return Config.AZURE_API_KEY

    @staticmethod
    def get_azure_endpoint():
        """Get Azure endpoint from environment"""
        if not Config.AZURE_ENDPOINT:
            raise ValueError(
                "AZURE_ENDPOINT not found in environment variables. "
                "Please check your .env file."
            )
        return Config.AZURE_ENDPOINT

    @staticmethod
    def get_azure_deployment_name():
        """Get Azure deployment name from environment"""
        if not Config.AZURE_DEPLOYMENT_NAME:
            raise ValueError(
                "AZURE_DEPLOYMENT_NAME not found in environment variables. "
                "Please check your .env file."
            )
        return Config.AZURE_DEPLOYMENT_NAME

    @staticmethod
    def validate_config():
        """Validate all required configuration"""
        try:
            Config.get_azure_api_key()
            Config.get_azure_endpoint()
            Config.get_azure_deployment_name()
            return True
        except ValueError as e:
            print(f"Configuration error: {e}")
            return False
    
    @staticmethod
    def get_config_summary():
        """Get a summary of current configuration"""
        return {
            'model_name': Config.MODEL_NAME,
            'max_concurrent_requests': Config.MAX_CONCURRENT_REQUESTS,
            'batch_size': Config.BATCH_SIZE,
            'default_excel_file': Config.DEFAULT_EXCEL_FILE,
            'default_address_column': Config.DEFAULT_ADDRESS_COLUMN,
            'default_output_file': Config.DEFAULT_OUTPUT_FILE,
            'use_grounding_by_default': Config.USE_GROUNDING_BY_DEFAULT
        }
