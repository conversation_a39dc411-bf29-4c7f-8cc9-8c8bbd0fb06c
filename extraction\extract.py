import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
import json
import asyncio

from extraction.llm import AzureAIClient
from extraction.model import VietnamAddress
from config import Config

class AddressExtractor:
    def __init__(self, max_concurrent_requests: int = None):
        """Initialize the address extractor with LLM client and concurrency control"""
        # Use config default if not provided
        max_requests = max_concurrent_requests or Config.MAX_CONCURRENT_REQUESTS
        self.llm_client = AzureAIClient(max_requests)
        self.max_workers = max_requests
        self.batch_size = Config.BATCH_SIZE
    
    def read_excel_file(self, file_path: str, address_column: str = 'Full_Address') -> pd.DataFrame:
        """Read addresses from Excel file"""
        try:
            df = pd.read_excel(file_path)
            if address_column not in df.columns:
                raise ValueError(f"Column '{address_column}' not found in Excel file. Available columns: {list(df.columns)}")
            return df
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            raise
    
    def extract_single_address(self, address: str) -> Optional[Dict[str, Any]]:
        """Extract components from a single address"""
        try:
            # Skip empty or blank addresses
            if not address or str(address).strip() in ['', '(Blank)', 'nan', 'None']:
                print(f"  Skipping blank/empty address")
                return None

            # Get structured extraction from LLM (returns dictionary after conversion)
            address_dict = self.llm_client.extract_address_components(address)
            return address_dict

        except Exception as e:
            print(f"Error extracting address '{address}': {e}")
            return None
    
    async def extract_single_address_async(self, address: str) -> Optional[Dict[str, Any]]:
        """Extract components from a single address asynchronously"""
        try:
            # Skip empty or blank addresses
            if not address or str(address).strip() in ['', '(Blank)', 'nan', 'None']:
                print(f"  Skipping blank/empty address")
                return None

            # Get structured extraction from LLM (returns dictionary after conversion)
            address_dict = await self.llm_client.extract_address_components_async(address)
            return address_dict

        except Exception as e:
            print(f"Error extracting address '{address}': {e}")
            return None

    def extract_batch_addresses(self, addresses: list) -> Optional[list]:
        """Extract components from a batch of addresses (up to 10)"""
        try:
            if not addresses or len(addresses) == 0:
                print("No addresses provided for batch processing")
                return []

            # Filter out empty addresses
            valid_addresses = []
            for addr in addresses:
                if addr and str(addr).strip() not in ['', '(Blank)', 'nan', 'None']:
                    valid_addresses.append(addr)

            if not valid_addresses:
                print("No valid addresses in batch")
                return []

            print(f"Processing batch of {len(valid_addresses)} addresses")

            # Get batch extraction from LLM
            results = self.llm_client.extract_batch_addresses(valid_addresses)
            return results

        except Exception as e:
            print(f"Error extracting address batch: {e}")
            return None

    async def extract_batch_addresses_async(self, addresses: list) -> Optional[list]:
        """Extract components from a batch of addresses asynchronously (up to 10)"""
        try:
            if not addresses or len(addresses) == 0:
                print("No addresses provided for batch processing")
                return []

            # Filter out empty addresses
            valid_addresses = []
            for addr in addresses:
                if addr and str(addr).strip() not in ['', '(Blank)', 'nan', 'None']:
                    valid_addresses.append(addr)

            if not valid_addresses:
                print("No valid addresses in batch")
                return []

            print(f"Processing batch of {len(valid_addresses)} addresses")

            # Get batch extraction from LLM
            results = await self.llm_client.extract_batch_addresses_async(valid_addresses)
            return results

        except Exception as e:
            print(f"Error extracting address batch: {e}")
            return None
    
    def extract_addresses_from_dataframe(self, df: pd.DataFrame, address_column: str = 'Full_Address',
                                       limit: Optional[int] = None,
                                       output_file: str = 'extracted_addresses.xlsx') -> List[Dict[str, Any]]:
        """Wrapper to run async extraction in sync context"""
        return asyncio.run(self.extract_addresses_from_dataframe_async(df, address_column, limit, output_file))
    
    async def extract_addresses_from_dataframe_async(self, df: pd.DataFrame, address_column: str = None,
                                                   limit: Optional[int] = None,
                                                   output_file: str = None) -> List[Dict[str, Any]]:
        """Extract address components from all addresses in dataframe asynchronously with periodic saving"""
        # Use config defaults if not provided
        address_column = address_column or Config.DEFAULT_ADDRESS_COLUMN
        output_file = output_file or Config.DEFAULT_OUTPUT_FILE
        
        addresses = df[address_column].dropna().tolist()
        
        # Apply limit if specified
        if limit and limit > 0:
            addresses = addresses[:limit]
            print(f"Processing limited to {limit} addresses")
        
        results = []
        total_addresses = len(addresses)
        
        # Process in batches for periodic saving
        for batch_start in range(0, total_addresses, self.batch_size):
            batch_end = min(batch_start + self.batch_size, total_addresses)
            batch_addresses = addresses[batch_start:batch_end]
            batch_num = (batch_start // self.batch_size) + 1
            
            print(f"\nProcessing batch {batch_num}: addresses {batch_start + 1} to {batch_end}")
            
            # Create tasks for concurrent processing
            tasks = []
            for i, address in enumerate(batch_addresses):
                actual_index = batch_start + i + 1
                print(f"Preparing task for address {actual_index}/{total_addresses}: {address}")
                
                # Get the original row index from DataFrame
                row_index = df[df[address_column] == address].index[0] if address in df[address_column].values else actual_index - 1
                row_index = int(row_index)
                
                task = self._process_single_address_with_metadata(address, row_index, actual_index, total_addresses)
                tasks.append(task)
            
            # Process batch concurrently
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions and add to results
            for result in batch_results:
                if isinstance(result, Exception):
                    print(f"Error in batch processing: {result}")
                elif result is not None:
                    results.append(result)
            
            # Save intermediate results every 500 extractions
            print(f"\nSaving intermediate results for batch {batch_num}...")
            self._save_intermediate_results(results[-len(batch_results):], output_file, batch_num)
            
            print(f"Batch {batch_num} completed. Total processed: {len(results)}")
            print("-" * 80)
        
        return results
    
    async def _process_single_address_with_metadata(self, address: str, row_index: int,
                                                  current_num: int, total_num: int) -> Optional[Dict[str, Any]]:
        """Process a single address with metadata asynchronously"""
        try:
            extracted = await self.extract_single_address_async(address)
            
            result = {
                'original_address': address,
                'row_index': row_index,
                'extracted_data': extracted,
                'success': extracted is not None
            }
            
            if extracted:
                print(f"✓ ({current_num}/{total_num}) Successfully extracted: {extracted}")
            else:
                print(f"✗ ({current_num}/{total_num}) Failed to extract address components")
            
            return result
            
        except Exception as e:
            print(f"✗ ({current_num}/{total_num}) Error processing '{address}': {e}")
            return {
                'original_address': address,
                'row_index': row_index,
                'extracted_data': None,
                'success': False
            }
    
    def _save_intermediate_results(self, results: List[Dict[str, Any]], output_file: str, batch_num: int):
        """Save intermediate results with batch number"""
        output_path = Path(output_file)
        intermediate_file = output_path.parent / f"{output_path.stem}_batch_{batch_num:03d}{output_path.suffix}"
        
        if output_path.suffix.lower() in ['.xlsx', '.xls']:
            self._save_as_excel(results, str(intermediate_file))
        else:
            self._save_as_json(results, str(intermediate_file))
        
        print(f"Intermediate results saved to {intermediate_file}")
    
    def save_results(self, results: List[Dict[str, Any]], output_file: str):
        """Save extraction results to file"""
        output_path = Path(output_file)
        
        if output_path.suffix.lower() == '.json':
            self._save_as_json(results, output_file)
        elif output_path.suffix.lower() in ['.xlsx', '.xls']:
            self._save_as_excel(results, output_file)
        else:
            raise ValueError("Output file must be .json, .xlsx, or .xls")
    
    def _save_as_json(self, results: List[Dict[str, Any]], output_file: str):
        """Save results as JSON"""
        # Convert VietnamAddress objects to dict for JSON serialization
        json_results = []
        for result in results:
            json_result = result.copy()
            if result['extracted_data'] and isinstance(result['extracted_data'], dict):
                json_result['extracted_data'] = result['extracted_data']
            json_results.append(json_result)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        print(f"Results saved to {output_file}")
    
    def _save_as_excel(self, results: List[Dict[str, Any]], output_file: str):
        """Save results as Excel"""
        flattened_results = []
        
        for result in results:
            flat_result = {
                'original_address': result['original_address'],
                'row_index': result['row_index'],
                'success': result['success']
            }
            
            # Flatten extracted data
            if result['extracted_data']:
                for key, value in result['extracted_data'].items():
                    if key == 'searched_fields':
                        # Convert list to comma-separated string for Excel
                        flat_result['extracted_searched_fields'] = ', '.join(value) if value else None
                    else:
                        flat_result[f'extracted_{key}'] = value
            else:
                # Add empty columns for failed extractions
                for field in ['unit', 'floor', 'building', 'street_number', 'street_name', 'ward_or_district', 'searched_fields', 'confidence_level']:
                    if field == 'searched_fields':
                        flat_result['extracted_searched_fields'] = None
                    else:
                        flat_result[f'extracted_{field}'] = None
            
            flattened_results.append(flat_result)
        
        df_results = pd.DataFrame(flattened_results)
        df_results.to_excel(output_file, index=False)
        print(f"Results saved to {output_file}")
    
    def get_extraction_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for extraction results"""
        total = len(results)
        successful = sum(1 for r in results if r['success'])
        failed = total - successful
        
        confidence_counts = {'High': 0, 'Medium': 0, 'Low': 0, 'Unknown': 0}

        for result in results:
            if result['success'] and result['extracted_data']:
                confidence = result['extracted_data'].get('confidence_level', 'Unknown')
                confidence_counts[confidence] = confidence_counts.get(confidence, 0) + 1

        return {
            'total_addresses': total,
            'successful_extractions': successful,
            'failed_extractions': failed,
            'success_rate': f"{(successful/total*100):.1f}%" if total > 0 else "0%",
            'confidence_distribution': confidence_counts
        }
