import os
from openai import AsyncAzureOpenAI
from typing import Optional, Dict, Any
import json
import numpy as np
import asyncio
from config import Config
from extraction.model import VietnamAddress  # Assuming this is your Pydantic model

class AzureAIClient:
    def __init__(self, max_concurrent_requests: int = None):
        """Initialize the Azure OpenAI client with concurrency control"""
        self.client = AsyncAzureOpenAI(
            api_key=Config.AZURE_API_KEY,
            api_version=Config.AZURE_API_VERSION,
            azure_endpoint=Config.AZURE_ENDPOINT,
            azure_deployment=Config.AZURE_DEPLOYMENT_NAME  # Needed for Azure
        )
        
        self.model_name = Config.AZURE_DEPLOYMENT_NAME  # Azure uses deployment names
        
        # Concurrency control
        max_requests = max_concurrent_requests or Config.MAX_CONCURRENT_REQUESTS
        self.semaphore = asyncio.Semaphore(max_requests)

    async def _make_azure_request(self, messages: list, response_format: dict = None, temperature: float = 0.7) -> Optional[str]:
        """Helper method to make Azure OpenAI requests with error handling"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=temperature,
                response_format=response_format,
                max_tokens=800
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"Azure OpenAI API error: {str(e)}")
            return None

    async def generate_content(self, query: str) -> Optional[str]:
        """Generate content for a single query"""
        messages = [{"role": "user", "content": query}]
        return await self._make_azure_request(messages)

    async def generate_content_async(self, query: str) -> Optional[str]:
        """Alias for generate_content for backward compatibility"""
        return await self.generate_content(query)

    def _convert_to_json_serializable(self, obj):
        """Convert numpy/pandas types to native Python types for JSON serialization"""
        if isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, (np.integer, np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.__dict__.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        return obj

    async def extract_address_components_async(self, address: str) -> Optional[Dict[str, Any]]:
        """
        Extract Vietnamese address components using Azure OpenAI with JSON response.
        
        Args:
            address: The Vietnamese address string to parse
            
        Returns:
            Dictionary with address components or None if failed
        """
        if address.strip() == "(Blank)":
            print(f"Skipping blank address: {address}")
            return None

        prompt = f"""
        Extract and structure this Vietnamese address into JSON components:
        Address: "{address}"

        Required fields:
        - unit: Apartment/unit number (e.g., "P.101", "Căn 205")
        - floor: Floor number (e.g., "3F", "Tầng 12")
        - building: Building name (e.g., "Vincom Center")
        - street_number: House/street number (e.g., "25A", "123/7")
        - street_name: Street name (e.g., "Nguyễn Trãi")
        - ward_or_district: Ward/District (e.g., "Phường Bến Nghé", "Quận 1")
        - confidence_level: "High", "Medium", or "Low"
        - searched_fields: Always null (not using web search)

        Rules:
        1. Return ONLY valid JSON
        2. Use null for missing fields
        3. Keep original Vietnamese text
        4. Don't invent information
        """

        messages = [{"role": "user", "content": prompt}]
        
        async with self.semaphore:
            try:
                # First try with JSON response format
                json_response = await self._make_azure_request(
                    messages,
                    response_format={"type": "json_object"},
                    temperature=0.3  # More deterministic for structured data
                )
                
                if json_response:
                    result = json.loads(json_response)
                    return self._convert_to_json_serializable(result)
                
                # Fallback to regular response if JSON mode fails
                text_response = await self._make_azure_request(messages)
                if text_response:
                    # Try to extract JSON from text response
                    try:
                        json_start = text_response.find('{')
                        json_end = text_response.rfind('}') + 1
                        json_str = text_response[json_start:json_end]
                        result = json.loads(json_str)
                        return self._convert_to_json_serializable(result)
                    except json.JSONDecodeError:
                        print(f"Failed to parse response for address: {address}")
                        return None
                
                return None

            except Exception as e:
                print(f"Error processing address '{address}': {str(e)}")
                return None