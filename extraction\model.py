from typing import List, Optional
from pydantic import BaseModel, Field
 
class VietnamAddress(BaseModel):
    """Structured model for Vietnamese address components with search tracking"""
    unit: Optional[str] = Field(
        None,
        description="Unit or apartment number (e.g., P.101, Căn 205, Phòng 12A)"
    )
    floor: Optional[str] = Field(
        None,
        description="Floor or level number (e.g., 3F, Tầng 12, 18/F, Lầu 5)"
    )
    building: Optional[str] = Field(
        None,
        description="Building or tower name (e.g., Vincom Center, Landmark 81)"
    )
    street_number: Optional[str] = Field(
        None,
        description="Street or house number (e.g., 25A, 123, 45/7)"
    )
    street_name: Optional[str] = Field(
        None,
        description="Street name (e.g., <PERSON><PERSON><PERSON>, Le <PERSON>, <PERSON><PERSON>)"
    )
    ward_or_district: Optional[str] = Field(
        None,
        description="Ward or District name (e.g., Phư<PERSON>ng Ben Nghe, Quận 1, <PERSON><PERSON><PERSON><PERSON> Trung Văn)"
    )
    searched_fields: Optional[List[str]] = Field(
        None,
        description="List of fields that were successfully extracted using internet search"
    )
    confidence_level: Optional[str] = Field(
        None,
        description="Confidence level of extraction: High, Mid, or Low"
    )
 
class VietnamAddressList(BaseModel):
    addresses: List[VietnamAddress] = Field(
        ...,
        description="List of structured Vietnamese address components"
    )