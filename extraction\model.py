from typing import List, Optional
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
 
class VietnamAddress(BaseModel):
    """Structured model for Vietnamese address components with search tracking"""
    unit: Optional[str] = Field(
        None,
        description="Unit or apartment number (e.g., P.101, Căn 205, Phòng 12A)"
    )
    floor: Optional[str] = Field(
        None,
        description="Floor or level number (e.g., 3F, Tầng 12, 18/F, Lầu 5)"
    )
    building: Optional[str] = Field(
        None,
        description="Building or tower name (e.g., Vincom Center, Landmark 81)"
    )
    street_number: Optional[str] = Field(
        None,
        description="Street or house number (e.g., 25A, 123, 45/7)"
    )
    street_name: Optional[str] = Field(
        None,
        description="Street name (e.g., <PERSON><PERSON><PERSON>rai, Le Lo<PERSON>, <PERSON>am <PERSON>)"
    )
    ward_or_district: Optional[str] = Field(
        None,
        description="Ward or District name (e.g., <PERSON><PERSON><PERSON><PERSON>, Quận 1, <PERSON><PERSON><PERSON><PERSON> Trung Văn)"
    )
    searched_fields: Optional[List[str]] = Field(
        None,
        description="List of fields that were successfully extracted using internet search"
    )
    confidence_level: Optional[str] = Field(
        None,
        description="Confidence level of extraction: High, Mid, or Low"
    )
 
class VietnamAddressList(BaseModel):
    addresses: List[VietnamAddress] = Field(
        ...,
        description="List of structured Vietnamese address components"
    )

# List of VietnamAddress objects
vietnamese_addresses: List[VietnamAddress] = [
    VietnamAddress(
        unit="P.101",
        floor="Tầng 12",
        building="Vincom Center",
        street_number="25A",
        street_name="Nguyen Trai",
        ward_or_district="Phường Ben Nghe, Quận 1"
    ),
    VietnamAddress(
        unit="Căn 205",
        floor="Lầu 5",
        building="Landmark 81",
        street_number="123",
        street_name="Le Loi",
        ward_or_district="Phường Trung Văn"
    ),
    VietnamAddress(
        unit="Phòng 12A",
        floor="3F",
        building="Saigon Center",
        street_number="45/7",
        street_name="Pham Ngoc Thach",
        ward_or_district="Quận 3"
    )
]

# Initialize LLM with LangChain
structured_llm = ChatOpenAI(model="gpt-4").with_structured_output(VietnamAddress)