from typing import Optional
from pydantic import BaseModel, Field

class VietnamAddress(BaseModel):
    """Structured model for Vietnamese address components"""

    unit: Optional[str] = Field(
        None,
        description="Unit or apartment number (e.g., P.101, Căn 205, Phòng 12A)"
    )
    floor: Optional[str] = Field(
        None,
        description="Floor or level number (e.g., 3F, Tầng 12, 18/F, Lầu 5)"
    )
    building: Optional[str] = Field(
        None,
        description="Building or tower name (e.g., Vincom Center, Landmark 81)"
    )
    street_number: Optional[str] = Field(
        None,
        description="Street or house number (e.g., 25A, 123, 45/7)"
    )
    street_name: Optional[str] = Field(
        None,
        description="Street name (e.g., <PERSON><PERSON><PERSON>rai, Le Lo<PERSON>, <PERSON>am <PERSON>)"
    )
    ward_or_district: Optional[str] = Field(
        None,
        description="Ward or District name (e.g., Phường <PERSON> Nghe, Quận 1, <PERSON><PERSON><PERSON><PERSON> Trung Văn)"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "unit": "P.101",
                "floor": "Tầng 12",
                "building": "Vincom Center",
                "street_number": "25A",
                "street_name": "Nguyen Trai",
                "ward_or_district": "Phường Ben Nghe, Quận 1"
            }
        }
