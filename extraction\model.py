from typing import Optional, List
from pydantic import BaseModel, Field

class VietnamAddress(BaseModel):
    """Structured model for Vietnamese address components"""

    unit: Optional[str] = Field(
        None,
        description="Unit or apartment number (e.g., P.101, Căn 205, Phòng 12A)"
    )
    floor: Optional[str] = Field(
        None,
        description="Floor or level number (e.g., 3F, Tầng 12, 18/F, Lầu 5)"
    )
    building: Optional[str] = Field(
        None,
        description="Building or tower name (e.g., Vincom Center, Landmark 81)"
    )
    street_number: Optional[str] = Field(
        None,
        description="Street or house number (e.g., 25A, 123, 45/7)"
    )
    street_name: Optional[str] = Field(
        None,
        description="Street name (e.g., <PERSON><PERSON><PERSON>rai, Le Lo<PERSON>, <PERSON>am <PERSON>)"
    )
    ward_or_district: Optional[str] = Field(
        None,
        description="Ward or District name (e.g., Ph<PERSON>ờng Ben Nghe, Quận 1, <PERSON><PERSON><PERSON><PERSON>rung Văn)"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "unit": "P.101",
                "floor": "Tầng 12",
                "building": "Vincom Center",
                "street_number": "25A",
                "street_name": "Nguyen Trai",
                "ward_or_district": "Phường Ben Nghe, Quận 1"
            }
        }


# Create a list of VietnamAddress objects
vietnamese_addresses: List[VietnamAddress] = [
    VietnamAddress(
        unit="P.101",
        floor="Tầng 12",
        building="Vincom Center",
        street_number="25A",
        street_name="Nguyen Trai",
        ward_or_district="Phường Ben Nghe, Quận 1"
    ),
    VietnamAddress(
        unit="Căn 205",
        floor="Lầu 5",
        building="Landmark 81",
        street_number="123",
        street_name="Le Loi",
        ward_or_district="Phường Trung Văn"
    ),
    VietnamAddress(
        unit="Phòng 12A",
        floor="3F",
        building="Saigon Center",
        street_number="45/7",
        street_name="Pham Ngoc Thach",
        ward_or_district="Quận 3"
    ),
    VietnamAddress(
        unit="Căn hộ 301",
        floor="Tầng 3",
        building="Vinhomes Central Park",
        street_number="208",
        street_name="Nguyen Huu Canh",
        ward_or_district="Phường 22, Quận Bình Thạnh"
    ),
    VietnamAddress(
        unit="Văn phòng 15B",
        floor="18/F",
        building="Bitexco Financial Tower",
        street_number="2",
        street_name="Hai Trieu",
        ward_or_district="Phường Ben Nghe, Quận 1"
    )
]

# LLM initialization removed - using Azure OpenAI client directly
