import argparse
from pathlib import Path

from extraction.extract import AddressExtractor
from config import Config

def main():
    # Validate configuration before starting
    if not Config.validate_config():
        print("Configuration validation failed. Please check your .env file.")
        return
    
    parser = argparse.ArgumentParser(description='Extract Vietnamese Address Components using Google GenAI')
    parser.add_argument('--excel-file', '-f', type=str, 
                       default=Config.DEFAULT_EXCEL_FILE,
                       help=f'Excel file containing addresses (default: {Config.DEFAULT_EXCEL_FILE})')
    parser.add_argument('--address-column', '-c', type=str,
                       default=Config.DEFAULT_ADDRESS_COLUMN,
                       help=f'Column name containing addresses (default: {Config.DEFAULT_ADDRESS_COLUMN})')
    parser.add_argument('--limit', '-l', type=int, default=None,
                       help='Limit the number of addresses to process')
    parser.add_argument('--skip', type=int, default=0,
                       help='Number of addresses to skip from the beginning')
    parser.add_argument('--output', '-o', type=str,
                       default=Config.DEFAULT_OUTPUT_FILE,
                       help=f'Output file (.json or .xlsx) (default: {Config.DEFAULT_OUTPUT_FILE})')

    parser.add_argument('--single-address', '-s', type=str,
                       help='Process a single address instead of Excel file')
    parser.add_argument('--batch-addresses', '-b', type=str, nargs='+',
                       help='Process a batch of addresses (up to 10) instead of Excel file')
    parser.add_argument('--max-concurrent', '-m', type=int, default=Config.MAX_CONCURRENT_REQUESTS,
                       help=f'Maximum concurrent API requests (default: {Config.MAX_CONCURRENT_REQUESTS})')
    parser.add_argument('--show-config', action='store_true',
                       help='Show current configuration and exit')
    
    args = parser.parse_args()
    
    # Show configuration if requested
    if args.show_config:
        print("Current Configuration:")
        print("=" * 50)
        config_summary = Config.get_config_summary()
        for key, value in config_summary.items():
            print(f"{key}: {value}")
        return
    
    # Initialize extractor with concurrency control
    extractor = AddressExtractor(max_concurrent_requests=args.max_concurrent)

    if args.single_address:
        # Process single address (synchronous for single address)
        print(f"Processing single address: {args.single_address}")
        result = extractor.extract_single_address(args.single_address)

        if result:
            print("\nExtraction Result:")
            print("{")
            for key, value in result.items():
                if value is not None:
                    print(f'  "{key}": "{value}",')
                else:
                    print(f'  "{key}": null,')
            print("}")
        else:
            print("Failed to extract address components")
        return

    if args.batch_addresses:
        # Process batch of addresses (up to 10)
        print(f"Processing batch of {len(args.batch_addresses)} addresses")
        if len(args.batch_addresses) > 10:
            print("Warning: Only first 10 addresses will be processed")

        results = extractor.extract_batch_addresses(args.batch_addresses)

        if results:
            print(f"\nBatch Extraction Results ({len(results)} addresses):")
            for i, result in enumerate(results, 1):
                print(f"\n--- Address {i} ---")
                print("{")
                for key, value in result.items():
                    if value is not None:
                        print(f'  "{key}": "{value}",')
                    else:
                        print(f'  "{key}": null,')
                print("}")
        else:
            print("Failed to extract address components from batch")
        return
    
    # Process Excel file
    excel_path = Path(args.excel_file)
    if not excel_path.exists():
        print(f"Error: Excel file '{args.excel_file}' not found")
        return
    
    try:
        # Read Excel file
        print(f"Reading addresses from: {args.excel_file}")
        df = extractor.read_excel_file(args.excel_file, args.address_column)
        print(f"Found {len(df)} rows in Excel file")
        
        # Extract addresses with async processing and periodic saving
        print(f"Starting async extraction (search grounding disabled)")
        print(f"Max concurrent requests: {args.max_concurrent}")
        if args.limit:
            print(f"Processing limit: {args.limit}")

        results = extractor.extract_addresses_from_dataframe(
            df, args.address_column, args.limit, args.output, args.skip
        )

        # Show clean results
        successful_results = [r for r in results if r['success'] and r['extracted_data']]
        for i, result in enumerate(successful_results, 1):
            print(f"\n--- Address {i} ---")
            print("{")
            data = result['extracted_data']
            for key, value in data.items():
                if key != 'searched_fields':  # Skip searched_fields
                    if value is not None:
                        print(f'  "{key}": "{value}",')
                    else:
                        print(f'  "{key}": null,')
            print("}")

        # Save final results
        extractor.save_results(results, args.output)
        
    except Exception as e:
        print(f"Error processing file: {e}")

if __name__ == "__main__":
    main()
