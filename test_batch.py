#!/usr/bin/env python3
"""
Test script for batch address processing
"""

from extraction.extract import AddressExtractor
from config import Config
import json

def test_batch_processing():
    """Test batch processing with 10 Vietnamese addresses"""
    
    # Validate configuration
    if not Config.validate_config():
        print("Configuration validation failed. Please check your .env file.")
        return
    
    # Sample Vietnamese addresses for testing
    test_addresses = [
        "P.101, Tầng 12, Vincom Center, 25A <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1",
        "Căn 205, <PERSON><PERSON><PERSON> 5, <PERSON> 81, 123 <PERSON>, <PERSON><PERSON><PERSON><PERSON> Trung Văn",
        "Phòng 12A, 3F, Saigon Center, 45/7 <PERSON><PERSON>, Quận 3",
        "<PERSON><PERSON><PERSON> 301, <PERSON><PERSON><PERSON> 3, Vinhomes Central Park, 208 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 22, <PERSON><PERSON><PERSON><PERSON> Bình Thạnh",
        "<PERSON><PERSON><PERSON> ph<PERSON> 15B, 18/F, Bitexco Financial Tower, 2 <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1",
        "<PERSON><PERSON><PERSON> 402, <PERSON><PERSON><PERSON> 4, Times City, 458 <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON> 8A, <PERSON><PERSON><PERSON> 8, Diamond Plaza, 34 <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1",
        "<PERSON><PERSON><PERSON> h<PERSON> 1205, <PERSON><PERSON><PERSON> 12, <PERSON><PERSON><PERSON><PERSON> Tower, <PERSON>6 <PERSON><PERSON>m <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> 501, <PERSON><PERSON><PERSON> 5, <PERSON><PERSON> <PERSON>, 54 <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON> 302, <PERSON><PERSON><PERSON> 3, <PERSON> <PERSON>, 91 <PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 22, <PERSON><PERSON><PERSON><PERSON> Bình Thạnh"
    ]
    
    print("=== Testing Batch Address Processing ===")
    print(f"Processing {len(test_addresses)} addresses in one request...")
    print()
    
    # Initialize extractor
    extractor = AddressExtractor(max_concurrent_requests=1)
    
    # Process batch
    results = extractor.extract_batch_addresses(test_addresses)
    
    if results:
        print(f"✅ Successfully processed {len(results)} addresses")
        print()
        
        for i, result in enumerate(results, 1):
            print(f"--- Address {i} ---")
            if 'address_index' in result:
                original_addr = test_addresses[result['address_index'] - 1] if result['address_index'] <= len(test_addresses) else "Unknown"
                print(f"Original: {original_addr}")
            print("Extracted:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            print()
    else:
        print("❌ Failed to process batch")

if __name__ == "__main__":
    test_batch_processing()
